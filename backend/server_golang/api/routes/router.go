package routes

import (
	"net/http"

	"server_golang/internal/handlers"
	"server_golang/internal/middleware"
	store "server_golang/internal/repository"
	"server_golang/internal/services"

	"github.com/gorilla/mux"
	"gorm.io/gorm"
)

func Router(db *gorm.DB) http.Handler {
	r := mux.NewRouter()
	r.Use(middleware.JWTAuthMiddleware)

	// database operations
	profiledb := store.NewProfileDbOperation(db)

	// service operation
	profileservice := services.NewprofileService(profiledb)

	// handler
	profilehandler := handlers.NewProfileHandler(profileservice)

	r.HandleFunc("/profile", profilehandler.GetProfileDetails).Methods("GET")

	return r
}
