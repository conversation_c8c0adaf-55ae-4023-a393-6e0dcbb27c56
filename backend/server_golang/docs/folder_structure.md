# Go  Folder Structure

This document explains the folder structure for the Go project.

```
server_golang/
├── cmd/
│   └── myapp/
│       └── main.go        # Entry point of the application
│
├── internal/
│   ├── config/            # Application configuration handling
│   ├── database/          # Database connection and queries
│   ├── handlers/          # HTTP handlers / controllers
│   ├── middleware/        # Middleware functions
│   ├── models/            # Data models / structs
│   ├── repository/        # Data persistence logic
│   ├── services/          # Business logic
│   └── utils/             # Helper utilities
│
├── pkg/                   # Public packages (shared across projects)
│   └── logger/            # Logging utilities
│
├── api/
│   ├── routes/            # API route definitions
│   ├── requests/          # Request DTOs
│   └── responses/         # Response DTOs
│
├── docs/                  # Documentation
│   └── folder_structure.md
│
├── test/                  # Test files, mocks, integration tests
│
├── go.mod
├── go.sum



```

---

## **1. cmd/**
Holds the entry points for the application.  
Each subfolder inside `cmd` is a separate executable.  
- Example: `cmd/main.go` contains the `main()` function.

---

## **2. internal/**
Contains private application code that is not intended to be used by external projects.

### **2.1 config/**
- Loads environment variables.
- Parses configuration files.
- Central place for app settings.

### **2.2 database/**
- Database connection setup.
- Migration scripts.
- Query execution functions.

### **2.3 handlers/**
- HTTP handler functions (controllers).
- Parse requests, call services, send responses.

### **2.4 middleware/**
- Functions that wrap HTTP handlers to add behavior (logging, authentication, etc.).

### **2.5 models/**
- Structs representing database tables or API objects.

### **2.6 repository/**
- Database interaction layer.
- Implements CRUD logic.

### **2.7 services/**
- Core business logic.
- Orchestrates repositories and other services.

### **2.8 utils/**
- Helper functions used across the application.

---

## **3. pkg/**
Contains code that can be shared across different projects.  
Example: logging utilities, reusable validators.

---

## **4. api/**
Handles the API layer of the project.

### **4.1 routes/**
- API route registration.

### **4.2 requests/**
- Request Data Transfer Objects (DTOs).
      Request DTOs (Data Transfer Objects). Structs used to decode incoming JSON or form data from the client. Example: type CreateUserRequest struct { Name string }

### **4.3 responses/**
- Response DTOs.
       Response DTOs. Structs used to send well-structured JSON/XML responses back to clients. Example: type UserResponse struct { ID int; Name string }

---

## **5. docs/**
Project documentation, including this file.

---

## **6. test/**
- Unit tests.
- Integration tests.
- Test mocks and fixtures.

---

## **7. go.mod & go.sum**
- `go.mod` defines the module path and dependencies.
- `go.sum` stores dependency checksums.

---

## **8. README.md**
Project description, setup instructions, and usage.
