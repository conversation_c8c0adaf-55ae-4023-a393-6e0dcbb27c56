package models

import "time"

type User struct {
	ID         uint      `gorm:"column:id;primaryKey;autoIncrement"`
	Email      string    `gorm:"column:email;not null;unique"`
	Username   string    `gorm:"column:username;not null;unique"`
	Password   string    `gorm:"column:password_hash;not null"`
	ProfilePic string    `gorm:"column:profile_pic"`
	Level      int       `gorm:"column:level;default:1"`
	Nickname   string    `gorm:"column:nickname"`
	CreatedAt  time.Time `gorm:"column:created_at;autoCreateTime"`
}
