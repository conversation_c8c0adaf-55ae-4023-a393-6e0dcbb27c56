package middleware

import (
	"net/http"

	"github.com/rs/cors" // Import the cors package
)

func CORSMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		cors.New(cors.Options{
			AllowedOrigins:   []string{"http://localhost:3000", "http://localhost:5173", "http://localhost:5174"},
			AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
			AllowedHeaders:   []string{"Authorization", "Content-Type"},
			AllowCredentials: true,
		}).Handler(next)
	})
}
