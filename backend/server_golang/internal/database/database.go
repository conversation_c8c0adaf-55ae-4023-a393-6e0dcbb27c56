package database

import (
	"log"

	"server_golang/internal/config"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// InitDB initializes the database connection and runs migrations
func InitDB() (*gorm.DB, error) {
	db, err := gorm.Open(postgres.Open(config.Config.DSN_CONNECTION), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	log.Println("✅ Connected to PostgreSQL successfully with GORM!")
	return db, nil
}
