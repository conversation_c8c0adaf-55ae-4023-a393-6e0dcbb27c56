package utils

import (
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v4"
)

type contextKey string

const UserID contextKey = "userID"

var (
	jwtSecret      = []byte("your-secret-key") // Use env var in production (e.g., os.Getenv("JWT_SECRET"))
	expectedIssuer = "your-app-name"           // Replace with your issuer
	expectedAud    = "your-audience"           // Replace with your expected audience
)

func ValidateJWT(tokenStr string) (jwt.MapClaims, error) {
	// Parse the token with claims validation
	token, err := jwt.Parse(tokenStr, func(token *jwt.Token) (interface{}, error) {
		// Check signing method (avoid alg=none attacks)
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return jwtSecret, nil
	})
	if err != nil {
		return nil, fmt.Errorf("token parsing failed: %w", err)
	}

	// Check if token is valid
	if !token.Valid {
		return nil, errors.New("invalid token")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("invalid claims format")
	}

	// Validate standard claims
	if err := validateClaims(claims); err != nil {
		return nil, err
	}

	return claims, nil
}

// validateClaims checks iss, aud, exp, nbf, etc.
func validateClaims(claims jwt.MapClaims) error {
	// Check expiration
	if exp, ok := claims["exp"].(float64); ok {
		if time.Now().Unix() > int64(exp) {
			return errors.New("token expired")
		}
	} else {
		return errors.New("missing or invalid exp claim")
	}

	// Check not-before (if present)
	if nbf, ok := claims["nbf"].(float64); ok {
		if time.Now().Unix() < int64(nbf) {
			return errors.New("token not valid yet")
		}
	}

	// Validate issuer (if expectedIssuer is set)
	if expectedIssuer != "" {
		if iss, ok := claims["iss"].(string); !ok || iss != expectedIssuer {
			return errors.New("invalid issuer")
		}
	}

	// Validate audience (critical for security)
	if expectedAud != "" {
		aud, ok := claims["aud"]
		if !ok {
			return errors.New("missing aud claim")
		}

		switch audVal := aud.(type) {
		case string:
			if audVal != expectedAud {
				return errors.New("invalid audience")
			}
		case []interface{}: // Handle array case (JWT spec allows aud to be an array)
			found := false
			for _, v := range audVal {
				if s, ok := v.(string); ok && s == expectedAud {
					found = true
					break
				}
			}
			if !found {
				return errors.New("invalid audience in aud array")
			}
		default:
			return errors.New("invalid aud claim format")
		}
	}

	return nil
}
