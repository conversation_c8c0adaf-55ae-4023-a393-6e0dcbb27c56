package main

import (
	"log"
	"net/http"

	"server_golang/api/routes"
	"server_golang/internal/database"
	"server_golang/internal/middleware"
)

func main() {
	db, err := database.InitDB()
	if err != nil {
		log.Fatal(err)
	}
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatal(err)
	}
	defer sqlDB.Close()

	handler := routes.Router(db)

	log.Println("server running on port 8080")

	if err := http.ListenAndServe(":8080", middleware.CORSMiddleware(handler)); err != nil {
		log.Fatal("ListenAndServe: ", err)
	}
}
