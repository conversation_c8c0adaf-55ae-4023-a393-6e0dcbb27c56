# Node.js Backend Architecture Documentation

## 1. Introduction

This document describes the architecture of a Node.js backend using
Express.js. The design follows a modular structure for scalability,
maintainability, and clarity.

## 2. Project Structure

The recommended folder structure is as follows:

```
server_node/
├── src/
│   ├── controllers/      # Business logic for each route (e.g., userController.js)
│   ├── routes/           # Route definitions (e.g., userRoutes.js)
│   ├── middleware/       # Custom middleware (e.g., authentication, error handling)
│   ├── models/           # Database models (e.g., User.js, Riddle.js). Contains definitions for how you interact with tables (ORM models, query functions, or classes). Table creation is handled elsewhere; models assume tables exist.
│   ├── config/           # Configuration files (e.g., database.js, environment variables)
│   ├── utils/            # Helper functions and utilities (e.g., password hashing, token generation)
│   └── server.js         # Main app entry point (Express app setup)
├── package.json          # Dependencies and scripts
├── .env                  # Environment variables
└── README.md             # Project documentation
```

### Folder/Files Explanation
- **controllers/**: Contains functions that implement business logic for each route. Controllers receive requests from routes, process data, interact with models, and return responses.
- **routes/**: Defines API endpoints and maps them to controller functions. Keeps routing logic separate from business logic.
- **middleware/**: Custom Express middleware for tasks like authentication, logging, error handling, and request validation.
- **models/**: Contains database models and query logic. Defines how your app interacts with the database (e.g., using SQLite, Sequelize, or plain SQL queries).
- **config/**: Stores configuration files, such as database connection setup and environment variable management.
- **utils/**: Helper functions and utilities used throughout the app (e.g., hashing, token generation, formatting).
- **server.js**: The main entry point that initializes the Express app, applies middleware, sets up routes, and starts the server.
- **package.json**: Lists dependencies, scripts, and project metadata.
- **.env**: Stores sensitive configuration like database URLs, JWT secrets, etc.
- **README.md**: Project overview and documentation.

## 3. Request Flow

1. **Client Request**: The client sends a request (e.g., POST /api/auth).
2. **Express App**: `server.js` receives the request and forwards it to the appropriate route handler.
3. **Router Layer**: The request is directed to the matching route file in the `/routes` folder.
4. **Controller Layer**: The controller executes the business logic, often calling services or models.
5. **Model Layer**: If database interaction is needed, models communicate with the database.
6. **Response**: The controller sends the response back to the client.

## 4. Example

Example: `/api/auth` registration flow:

1. **Route**: `routes/auth.routes.js` contains `router.post('/auth', register);`.
2. **Controller**: `controllers/auth.controller.js` has the `register` function to handle the logic.
3. **Database**: The controller may use a `User` model to store user data.
4. **Response**: The controller returns a success message or an error.

## 5. Error Handling

A central error handler middleware is used to catch and handle all errors:

```javascript
// middleware/errorHandler.js
export const errorHandler = (err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ success: false, message: err.message || 'Internal server error' });
};
```

This ensures consistent error responses across the application.

## 6. Benefits of This Architecture

- **Modular**: Easy to maintain and scale.
- **Separation of Concerns**: Routes, controllers, and models are independent.
- **Reusability**: Middleware and utilities can be reused across routes.
- **Testability**: Easier to write unit and integration tests.
