import {prisma} from '../config/db.js';

export const insertUser = async (user) => {
    return await prisma.user.create({
        data: user
    })
}


export const getUserByEmail = async (email) => {
    return await prisma.user.findUnique({
        where: {
            email: email
        }
    })
}

export const getUserByUsername = async (username) => {
    return await prisma.user.findUnique({
        where: {
            username: username
        }
    })
}
