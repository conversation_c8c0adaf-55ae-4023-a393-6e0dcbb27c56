import jwt from 'jsonwebtoken';
import { insertUser, getUserByEmail, getUserByUsername } from '../models/user.model.js';
import { hashPassword, comparePassword } from '../utils/hash.js';
import { validateEmail, validatePassword } from '../utils/validators.js';
import { config } from '../config/env.js';

export const register = async (req, res) => {
  try {
    const { username, email, password, avatar } = req.body;

    if (!username || !email || !password) {
      return res.status(400).json({ success: false, message: 'Username, email, and password are required' });
    }
    if (username.length < 3) {
      return res.status(400).json({ success: false, message: 'Username must be at least 3 characters long' });
    }
    if (!validateEmail(email)) {
      return res.status(400).json({ success: false, message: 'Invalid email address' });
    }
    if (!validatePassword(password)) {
      return res.status(400).json({ success: false, message: 'Weak password' });
    }

    if (await getUserByEmail(email)) {
      return res.status(409).json({ success: false, message: 'Email already exists' });
    }
    if (await getUserByUsername(username)) {
      return res.status(409).json({ success: false, message: 'Username already taken' });
    }

    const hashedPassword = await hashPassword(password);

    const result = await insertUser({ email, username, password: hashedPassword, profile_pic: avatar || 'default image', nickname: username });

    res.status(201).json({
      success: true,
      message: 'Account created successfully!',
      user: { id: result.id, username, email, avatar: avatar || 'default image' }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const login = async (req, res) => {
  try {
    const { email, password } = req.body;
    if (!email || !password) return res.status(400).json({ success: false, message: 'Email and password required' });

    const user = await getUserByEmail(email);
    if (!user) return res.status(401).json({ success: false, message: 'Invalid credentials' });

    const isValid = await comparePassword(password, user.password);
    if (!isValid) return res.status(401).json({ success: false, message: 'Invalid credentials' });

    const token = jwt.sign({ userId: user.id }, config.jwtSecret, { expiresIn: '1h' });

    res.json({ success: true, message: 'Login successful', token });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
};
