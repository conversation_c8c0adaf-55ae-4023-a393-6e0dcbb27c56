import express from 'express';
import cors from 'cors';
import authRoutes from './routes/auth.routes.js';
import { errorHandler } from './middleware/error.middleware.js';

const app = express();

app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5173', 'http://localhost:8080', 'http://localhost:5174', 'http://********:3000'],
  credentials: true
}));
app.use(express.json());

app.use('/api', authRoutes);

app.use(errorHandler);

export default app;
