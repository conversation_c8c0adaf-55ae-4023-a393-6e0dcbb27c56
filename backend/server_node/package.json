{"name": "backend", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\"", "lint": "eslint ."}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.14.0", "bcryptjs": "^3.0.2", "better-sqlite3": "^11.10.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "pg": "^8.16.3"}, "devDependencies": {"eslint": "^9.33.0", "prisma": "^6.14.0"}}