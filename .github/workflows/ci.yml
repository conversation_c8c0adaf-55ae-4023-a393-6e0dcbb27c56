# GitHub Actions workflow for backend (Golang & Node.js) and frontend (lint)

name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  backend-golang:
    name: Backend Golang Test
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: backend/server_golang
    steps:
      - uses: actions/checkout@v4
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.21'
      - name: Install dependencies
        run: go mod download
      - name: Run tests
        run: |
          if ls *_test.go 1> /dev/null 2>&1; then
            go test ./...
          else
            echo "No Go tests found."
          fi

  backend-node:
    name: Backend Node.js Test
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: backend/server_node
    steps:
      - uses: actions/checkout@v4
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
      - name: Install dependencies
        run: npm install
      - name: Run tests (only if test script exists)
        run: |
          if [ -f package.json ] && grep -q '"test":' package.json && ! grep -q 'no test specified' package.json; then
            npm test
          else
            echo "No Node.js tests found."
          fi

  frontend-lint:
    name: Frontend Lint
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: front_end
    steps:
      - uses: actions/checkout@v4
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
      - name: Install dependencies
        run: npm install
      - name: Run ESLint (only if lint script exists)
        run: |
          if [ -f package.json ] && grep -q '"lint":' package.json; then
            npm run lint
          else
            echo "No lint script found."
          fi
