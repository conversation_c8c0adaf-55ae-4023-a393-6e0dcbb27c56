{"name": "riddle_tod", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently -k -n vite,go,node -c cyan,green,magenta \"npm run serve:vite\" \"npm run serve:go\" \"npm run serve:node\"", "serve:vite": "vite", "serve:go": "cd ../backend/server_golang && go run .", "serve:node": "cd ../backend/server_node && node server.js", "lint": "eslint ."}, "dependencies": {"concurrently": "^9.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}