import React, { useState, useEffect } from 'react';
import './DashboardPage.css';

const DashboardPage = () => {
  const [showExtras, setShowExtras] = useState(false);
  const [userData, setUserData] = useState(null);

  useEffect(() => {
    // Define the async function inside useEffect
    const fetchUser = async () => {
      const token = localStorage.getItem("token"); // Get the token from local storage

      if (!token) return;

      try {
        const res = await fetch("http://localhost:8080/dashboardDetails", {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!res.ok) throw new Error("Failed to fetch user");

        const user = await res.json();

    // Example fallback data (for testing)
    // const fallback = {
    //   profile: "/profile_pics/test.jpg",
    //   username: "<PERSON>",
    //   level: 15,
    // };
         console.log(user)
    setUserData(user);
  } catch (err) {
    console.error(err);
  }
};

fetchUser();

   
  }, []);

  return (
    <div className="container-dashboard">
      <div className="header">
        <div className="logo">🎯 Riddle & Dare</div>
        <div className="subtitle">Challenge Your Mind & Courage</div>
      </div>

      {!showExtras ? (
        <MainMenu userData={userData} setShowExtras={setShowExtras} />
      ) : (
        <ExtrasMenu setShowExtras={setShowExtras} />
      )}
    </div>
  );
};

const MainMenu = ({ userData, setShowExtras }) => (
  <div id="mainMenu">
    {userData && (
      <div className="user-profile">
       <div className="profile-pic"><img src={userData.profile} alt='profilepic'/></div>
        
        <div className="user-info">
          <div className="username">{userData.Username}</div>
          <div className="level-info">
            <div className="level">Level {userData.level}</div>
            <div className="nickname">🏆 Riddle Master</div>
          </div>
        </div>
      </div>
    )}

    <div className="menu-grid">
      <div className="menu-item">
        <div className="menu-icon solo">🧩</div>
        <div className="menu-title">Solo Riddle Mode</div>
        <div className="menu-desc">Challenge yourself with mind-bending riddles and puzzles</div>
      </div>
      <div className="menu-item">
        <div className="menu-icon multiplayer">👥</div>
        <div className="menu-title">Local Multiplayer Riddles</div>
        <div className="menu-desc">Compete with friends on the same device</div>
      </div>
      <div className="menu-item">
        <div className="menu-icon truth-dare">🎭</div>
        <div className="menu-title">Truth or Dare</div>
        <div className="menu-desc">Classic party game with friends locally</div>
      </div>
      <div className="menu-item coming-soon">
        <div className="menu-icon multiplayer">🌐</div>
        <div className="menu-title">Online Multiplayer</div>
        <div className="menu-desc">Play with friends online - Coming Soon!</div>
        <div className="coming-soon-badge">Soon</div>
      </div>
      <div className="menu-item">
        <div className="menu-icon riddle-dare">⚡</div>
        <div className="menu-title">Riddle & Dare</div>
        <div className="menu-desc">Ultimate challenge combining riddles with dares</div>
      </div>
      <div className="menu-item" onClick={() => setShowExtras(true)}>
        <div className="menu-icon extras">⚙️</div>
        <div className="menu-title">Extras</div>
        <div className="menu-desc">Settings, progress, leaderboards and more</div>
      </div>
    </div>
  </div>
);

const ExtrasMenu = ({ setShowExtras }) => (
  <div id="extrasMenu" className="extras-menu active">
    <button className="back-btn" onClick={() => setShowExtras(false)}>
      <span>←</span> Back to Main Menu
    </button>

    <div className="extras-grid">
      <div className="extras-item">
        <div className="extras-icon progress">📊</div>
        <div className="menu-title">My Progress</div>
        <div className="menu-desc">View your achievements and stats</div>
      </div>
      <div className="extras-item">
        <div className="extras-icon leaderboard">🏆</div>
        <div className="menu-title">Leaderboard</div>
        <div className="menu-desc">See top players and rankings</div>
      </div>
      <div className="extras-item">
        <div className="extras-icon settings">⚙️</div>
        <div className="menu-title">Settings</div>
        <div className="menu-desc">Customize your game experience</div>
      </div>
      <div className="extras-item">
        <div className="extras-icon help">❓</div>
        <div className="menu-title">How to Play</div>
        <div className="menu-desc">Learn the rules and get tips</div>
      </div>
      <div className="extras-item">
        <div className="extras-icon logout">🚪</div>
        <div className="menu-title">Logout</div>
        <div className="menu-desc">Sign out of your account</div>
      </div>
    </div>
  </div>
);

export default DashboardPage;
